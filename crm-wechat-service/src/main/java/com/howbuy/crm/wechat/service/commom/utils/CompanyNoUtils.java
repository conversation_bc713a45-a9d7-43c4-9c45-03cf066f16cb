/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.utils;

import com.howbuy.crm.wechat.client.base.BaseCompanyNoRequest;
import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.service.service.config.CompanyConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: 企业编码处理工具类
 * <AUTHOR>
 * @date 2025-08-13 16:45:36
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CompanyNoUtils {

    private static CompanyConfigManager companyConfigManager;

    @Autowired
    public void setCompanyConfigManager(CompanyConfigManager companyConfigManager) {
        CompanyNoUtils.companyConfigManager = companyConfigManager;
    }
    
    /**
     * 默认企业编码（好买财富）
     */
    private static final String DEFAULT_COMPANY_NO = "1";
    
    /**
     * 从请求中获取企业编码，支持默认值
     * @param request 请求对象
     * @return 企业编码
     */
    public static String getCompanyNo(BaseCompanyNoRequest request) {
        if (request == null) {
            log.warn("请求对象为空，使用默认企业编码: {}", DEFAULT_COMPANY_NO);
            return DEFAULT_COMPANY_NO;
        }
        return getCompanyNo(request.getCompanyNo());
    }
    
    /**
     * 处理企业编码，支持默认值和兼容性
     * @param companyNo 企业编码
     * @return 处理后的企业编码
     */
    public static String getCompanyNo(String companyNo) {
        if (StringUtils.isBlank(companyNo)) {
            log.warn("企业编码为空，使用默认值: {}", DEFAULT_COMPANY_NO);
            return DEFAULT_COMPANY_NO;
        }
        return companyNo.trim();
    }
    
    /**
     * 兼容CompanyNoEnum的转换方法
     * @param companyNoEnum 企业编码枚举
     * @return 企业编码字符串
     * @deprecated 建议直接使用字符串形式的企业编码
     */
    @Deprecated
    public static String fromCompanyNoEnum(CompanyNoEnum companyNoEnum) {
        if (companyNoEnum == null) {
            log.warn("CompanyNoEnum为空，使用默认值: {}", DEFAULT_COMPANY_NO);
            return DEFAULT_COMPANY_NO;
        }
        return companyNoEnum.getCode();
    }

    /**
     * 兼容转换为CompanyNoEnum（逐步废弃）
     * @param companyNo 企业编码
     * @return 企业编码枚举
     * @deprecated 建议直接使用字符串形式的企业编码
     */
    @Deprecated
    public static CompanyNoEnum toCompanyNoEnum(String companyNo) {
        companyNo = getCompanyNo(companyNo);
        CompanyNoEnum enumValue = CompanyNoEnum.getEnum(companyNo);
        if (enumValue == null) {
            log.warn("无法转换为CompanyNoEnum，companyNo: {}，使用默认值", companyNo);
            return CompanyNoEnum.HOWBUY_WEALTH;
        }
        return enumValue;
    }



    /**
     * 验证企业编码是否为有效值（基于数据库配置）
     * @param companyNo 企业编码
     * @return 是否为有效的企业编码
     */
    public static boolean isValidEnumValue(String companyNo) {
        if (StringUtils.isBlank(companyNo)) {
            return false;
        }

        // 优先使用数据库配置验证
        if (companyConfigManager != null) {
            return companyConfigManager.isValidCompanyNo(companyNo.trim());
        }

        // 降级到枚举验证（向后兼容）
        log.warn("CompanyConfigManager 未初始化，降级使用枚举验证，companyNo: {}", companyNo);
        return CompanyNoEnum.getEnum(companyNo.trim()) != null;
    }
    
    /**
     * 获取默认企业编码
     * @return 默认企业编码
     */
    public static String getDefaultCompanyNo() {
        return DEFAULT_COMPANY_NO;
    }
    
    /**
     * 获取企业编码描述（基于数据库配置）
     * @param companyNo 企业编码
     * @return 企业编码描述
     */
    public static String getCompanyDescription(String companyNo) {
        companyNo = getCompanyNo(companyNo);

        // 优先使用数据库配置获取描述
        if (companyConfigManager != null) {
            try {
                String description = companyConfigManager.getCompanyDescription(companyNo);
                if (StringUtils.isNotBlank(description) && !"未知企业".equals(description)) {
                    return description;
                }
            } catch (Exception e) {
                log.warn("从数据库获取企业描述失败，companyNo: {}，错误: {}", companyNo, e.getMessage());
            }
        }

        // 降级到枚举获取描述（向后兼容）
        log.warn("CompanyConfigManager 未初始化或获取失败，降级使用枚举获取描述，companyNo: {}", companyNo);
        String description = CompanyNoEnum.getDescription(companyNo);
        return description != null ? description : "未知企业";
    }
    
    /**
     * 安全地从请求中获取企业编码，确保不为null
     * @param request 请求对象
     * @return 企业编码（保证不为null）
     */
    public static String safeGetCompanyNo(BaseCompanyNoRequest request) {
        String companyNo = getCompanyNo(request);
        return companyNo != null ? companyNo : DEFAULT_COMPANY_NO;
    }
    
    /**
     * 检查企业编码是否为默认值
     * @param companyNo 企业编码
     * @return 是否为默认值
     */
    public static boolean isDefaultCompanyNo(String companyNo) {
        return DEFAULT_COMPANY_NO.equals(getCompanyNo(companyNo));
    }
}
