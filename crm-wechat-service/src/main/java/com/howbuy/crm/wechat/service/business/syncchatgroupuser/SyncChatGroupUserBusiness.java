/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.business.syncchatgroupuser;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.page.PageMethod;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.wechat.client.enums.CompanyNoEnum; // 临时保留用于业务逻辑转换
import com.howbuy.crm.wechat.dao.bo.EmpDeptInfoBo;
import com.howbuy.crm.wechat.dao.po.CmWechatGroupPO;
import com.howbuy.crm.wechat.service.commom.utils.CompanyNoUtils;
import com.howbuy.crm.wechat.dao.po.CmWechatGroupUserPO;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.domain.externaluser.ExternalGroupChatUserDTO;
import com.howbuy.crm.wechat.service.domain.externaluser.ExternalGroupInfoDTO;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WechatExternalContactOuterService;
import com.howbuy.crm.wechat.service.repository.CmWechatEmpRepository;
import com.howbuy.crm.wechat.service.repository.CmWechatGroupRepository;
import com.howbuy.crm.wechat.service.repository.CmWechatGroupUserRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 同步所有企微客户群的成员信息
 * <AUTHOR>
 * @date 2023/10/30 14:36
 * @since JDK 1.8
 */

@Slf4j
@Component
public class SyncChatGroupUserBusiness {

    @Autowired
    private CmWechatEmpRepository cmWechatEmpRepository;

    @Autowired
    private CmWechatGroupRepository cmWechatGroupRepository;

    @Autowired
    private CmWechatGroupUserRepository cmWechatGroupUserRepository;

    @Autowired
    private WechatExternalContactOuterService wechatExternalContactOuterService;

    /**
     * @description: 拉取企微客户群的详情，并写入到数据库中
     * @param companyNo 企业编码
     * @param chatId 客户群ID
     * @param eventTime 事件时间
     * @return void
     * @author: jin.wang03
     * @date: 2023/10/31 15:17
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncGroupChatUserByCompanyNoAndChatId(String companyNo,
                                                      String chatId,
                                                      String eventTime) {
        // 使用CompanyNoUtils处理企业编码
        String processedCompanyNo = CompanyNoUtils.getCompanyNo(companyNo);
        log.info("companyNo:{}，开始-拉取chatId:[{}]的客户群详情！", processedCompanyNo, chatId);
        ExternalGroupInfoDTO externalGroupInfoDTO = wechatExternalContactOuterService.getGroupChatUserByChatId(processedCompanyNo, chatId);
        if (Objects.isNull(externalGroupInfoDTO)) {
            log.info("companyNo:{}，chatId:[{}]的客户群详情为空！", processedCompanyNo, chatId);
            return;
        }

        if (StringUtils.isNotEmpty(externalGroupInfoDTO.getErrCode()) && !"0".equals(externalGroupInfoDTO.getErrCode())) {
            log.error("companyNo:{}，拉取chatId:[{}]的客户群详情失败！errCode:[{}] errMsg:[{}]",
                    processedCompanyNo, chatId,
                    externalGroupInfoDTO.getErrCode(), externalGroupInfoDTO.getErrMsg());
            return;
        }

        // 更新客户群信息
        updateGroupInfo(externalGroupInfoDTO, processedCompanyNo);
        // 更新客户群用户信息
        updateGroupUsers(externalGroupInfoDTO, eventTime, processedCompanyNo);
        log.info("companyNo:{}，完成-拉取chatId:[{}]的客户群详情！", processedCompanyNo, chatId);
    }

    /**
     * @description: 拉取企微客户群的详情，并写入到数据库中
     * @param companyNoEnum  企微-企业主体
     * @param chatId 客户群ID
     * @param eventTime 事件时间
     * @return void
     * @author: jin.wang03
     * @date: 2023/10/31 15:17
     * @since JDK 1.8
     * @deprecated 请使用 {@link #syncGroupChatUserByCompanyNoAndChatId(String, String, String)} 方法
     */
    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public void syncGroupChatUserByCompanyNoAndChatId(CompanyNoEnum companyNoEnum,
                                                      String chatId,
                                                      String eventTime) {
        String companyNo = CompanyNoUtils.fromCompanyNoEnum(companyNoEnum);
        syncGroupChatUserByCompanyNoAndChatId(companyNo, chatId, eventTime);
    }


    /**
     * @param groupInfoDTO 客户群信息
     * @param companyNo  企业编码
     * @description: 更新客户群信息
     * @author: jin.wang03
     * @date: 2023/10/27 17:03
     * @since JDK 1.8
     */
    private void updateGroupInfo(ExternalGroupInfoDTO groupInfoDTO,
                                 String companyNo) {
        // 客户群ID
        String chatId = groupInfoDTO.getChatId();
        // 群名
        String chatName = groupInfoDTO.getName();
        // 群主ID
        String owner = groupInfoDTO.getOwner();
        // 群创建时间
        Date createTime = groupInfoDTO.getCreateTime();
        // 群状态
        String status = groupInfoDTO.getStatus();

        CmWechatGroupPO dealWechatGroupPo = cmWechatGroupRepository.getByChatId(chatId,companyNo);
        if(Objects.isNull(dealWechatGroupPo)){
            dealWechatGroupPo = new CmWechatGroupPO();
            //插入
            dealWechatGroupPo.setChatName(chatName);
            dealWechatGroupPo.setChatId(chatId);
            dealWechatGroupPo.setChatOwner(owner);
            dealWechatGroupPo.setCreateTime(createTime);
            dealWechatGroupPo.setStatus(status);
            dealWechatGroupPo.setCompanyNo(companyNo);
            cmWechatGroupRepository.batchInsert(Lists.newArrayList(dealWechatGroupPo));
            log.info("companyNo:{}，新增群信息，chatId:{}，新增信息：{}！", companyNo, chatId, JSON.toJSONString(dealWechatGroupPo));
        }
        if (!chatName.equals(dealWechatGroupPo.getChatName()) || !owner.equals(dealWechatGroupPo.getChatOwner())
                || !(createTime.equals(dealWechatGroupPo.getCreateTime()))) {
            CmWechatGroupPO updateWechatGroupPo = new CmWechatGroupPO();
            updateWechatGroupPo.setChatId(chatId);
            updateWechatGroupPo.setChatName(chatName);
            updateWechatGroupPo.setChatOwner(owner);
            updateWechatGroupPo.setCreateTime(createTime);
            updateWechatGroupPo.setUpdateTime(new Date());
            cmWechatGroupRepository.updateByChatId(updateWechatGroupPo);
            log.info("companyNo:{}，更新群信息，chatId:{}，更新信息：{}！", companyNo, chatId, JSON.toJSONString(updateWechatGroupPo));
        }

        // 查询员工表（CM_WECHAT_EMP）,根据群主ID查询员工信息，如果员工信息中的部门ID和部门名称与客户群列表表中的不一致，则更新
        EmpDeptInfoBo empDeptInfoBo = cmWechatEmpRepository.getDeptInfoByEmpId(companyNo,owner);
        if (empDeptInfoBo  == null) {
            log.error("companyNo:{}，根据chatId:{}的群主empId:[{}]，查询不到员工信息！",
                    companyNo,chatId,owner);
            return;
        }

        if (empDeptInfoBo.getDeptId() != dealWechatGroupPo.getDeptId() ||
                !StringUtils.equals(empDeptInfoBo.getDeptName(), dealWechatGroupPo.getDeptName())) {
            CmWechatGroupPO updateWechatGroupPo = new CmWechatGroupPO();
            updateWechatGroupPo.setChatId(chatId);
            updateWechatGroupPo.setDeptId(empDeptInfoBo.getDeptId());
            updateWechatGroupPo.setDeptName(empDeptInfoBo.getDeptName());
            updateWechatGroupPo.setUpdateTime(new Date());
            cmWechatGroupRepository.updateByChatId(updateWechatGroupPo);
            log.info("companyNo:{}，更新群信息，chatId:{}，更新信息：{}！", companyNo, chatId, JSON.toJSONString(updateWechatGroupPo));
        }
    }


    /**
     * @description: 将客户群详情写入到数据库中
     * @param groupInfoDTO	客户群详情
     * @param eventTime	事件创建时间  当作退群时间
     * @param companyNo  企业编码
     * @author: jin.wang03
     * @date: 2023/10/26 11:03
     * @since JDK 1.8
     */
    private void updateGroupUsers(ExternalGroupInfoDTO groupInfoDTO,
                                  String eventTime,
                                  String companyNo) {
        String chatId = groupInfoDTO.getChatId();
        //查找已存在的 群组用户
        List<CmWechatGroupUserPO> existsGroupUserPos = getCmWechatGroupUserPos(chatId);
        //已存在的 群组用户信息Map key:externalUserId value: 用户群组
        Map<String, CmWechatGroupUserPO> existsGroupUserMap =
                existsGroupUserPos.stream()
                        .collect(Collectors.toMap(CmWechatGroupUserPO::getExternalUserId,
                        Function.identity()));

        List<ExternalGroupChatUserDTO> memberList = groupInfoDTO.getMemberList();

        List<CmWechatGroupUserPO> bathInsertList = new ArrayList<>();
        for (ExternalGroupChatUserDTO chatUserDTO : memberList) {
            String userId = chatUserDTO.getUserId();
            String type = chatUserDTO.getType();
            String unionId = Optional.ofNullable(chatUserDTO.getUnionId()).orElse("");
            Date joinTime = chatUserDTO.getJoinTime();
            String joinScene = chatUserDTO.getJoinScene();
            String invitor = chatUserDTO.getInvitor();
            String groupNickname = Optional.ofNullable(chatUserDTO.getGroupNickname()).orElse("");
            String name = chatUserDTO.getName();

            // 本地数据库有数据。 有可能是有效、无效。
            if (existsGroupUserMap.containsKey(userId)) {
                // 判断数据库中的数据和查询到的数据是否一致，如果不一致，则更新
                CmWechatGroupUserPO existGroupUser = existsGroupUserMap.get(userId);
                // 判断客户类型、unionid、入群方式、在群的昵称、名称、群成员状态字段是否有变更，若有变更则需要更新
                if (!existGroupUser.getType().equals(type) || !existGroupUser.getUnionId().equals(unionId)
                        || !existGroupUser.getJoinScene().equals(joinScene) || !existGroupUser.getGroupNickName().equals(groupNickname)
                        || !existGroupUser.getName().equals(name) || !existGroupUser.getUserChatFlag().equals("0")) {

                    CmWechatGroupUserPO cmWechatGroupUserPo = CmWechatGroupUserPO.builder().chatId(chatId).externalUserId(userId)
                            .type(type).unionId(unionId).joinScene(joinScene).userChatFlag("0").invitor(invitor)
                            .groupNickName(groupNickname).name(name).joinTime(joinTime)
                            .updateTime(new Date()).build();
                    cmWechatGroupUserRepository.updateByChatIdAndUserId(cmWechatGroupUserPo);
                } else {
                    CmWechatGroupUserPO cmWechatGroupUserPo = CmWechatGroupUserPO.builder().chatId(chatId).externalUserId(userId)
                            .updateTime(new Date()).build();
                    cmWechatGroupUserRepository.updateByChatIdAndUserId(cmWechatGroupUserPo);
                }
            } else {
                //构建新增 群成员关系
                CmWechatGroupUserPO addGroupUserPo =
                        CmWechatGroupUserPO
                                .builder()
                                .chatId(chatId)
                                .externalUserId(userId)
                                .type(type)
                                .unionId(unionId)
                                .joinScene(joinScene)
                                .userChatFlag("0")
                                .invitor(invitor)
                                .groupNickName(groupNickname)
                                .name(name)
                                .joinTime(joinTime)
                                .companyNo(companyNo)
                                .createTime(new Date())
                                .build();
                bathInsertList.add(addGroupUserPo);
            }
        }

        if (!bathInsertList.isEmpty()) {
            cmWechatGroupUserRepository.batchInsert(bathInsertList);
        }

        // 对于客户群详情表（CM_WECHAT_GROUP_DETAIL）中的数据，如果在查询到的客户群详情中不存在，则将状态置为失效
        Set<String> currUserIdSet = memberList.stream().map(ExternalGroupChatUserDTO::getUserId).collect(Collectors.toSet());
        List<String> batchDeleteList = existsGroupUserMap.keySet().stream()
                .filter(preUserId -> !currUserIdSet.contains(preUserId)).collect(Collectors.toList());
        if (!batchDeleteList.isEmpty()) {
            Date leaveTime = buildLeaveTime(eventTime);
            log.info("companyNo：{}，更新退群时间leaveTime={}, chatid={}, userids={}",
                    companyNo,
                    leaveTime==null?null: DateUtil.date2String(leaveTime,DateUtil.DEFAULT_DATESFM),
                    chatId, batchDeleteList);
            //此处更新的是还在群的用户  如果之前已经退群了 则不再更新
            cmWechatGroupUserRepository.batchDeleteByChatIdAndUserId(chatId, batchDeleteList, leaveTime);
        }
    }

    /**
     * @description  退群时间 string转date，兼容时间戳是否已经乘过1000的情况
     * @param leaveTimeStr 时间戳字符串
     * @return Date 退群时间
     * <AUTHOR>
     * @date 2025-06-18 16:55:31
     * @since JDK 1.8
     */
    private Date buildLeaveTime(String leaveTimeStr) {
        if (StringUtils.isNotEmpty(leaveTimeStr)) {
            try {
                long timestamp = new BigDecimal(leaveTimeStr).longValue();
                
                // 判断时间戳是否已经是毫秒级别（13位数字）
                // 如果是秒级时间戳（10位数字），则需要乘以1000转换为毫秒
                if (String.valueOf(timestamp).length() <= 11) {
                    timestamp = timestamp * 1000;
                }
                
                return new Date(timestamp);
            } catch (Exception e) {
                log.error("转换退群时间异常:{}", Throwables.getStackTraceAsString(e));
            }
        }
        return null;
    }


    /**
     * @description: 根据客户群ID查询客户群详情
     * @param chatId 客户群ID
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatGroupUserPo> 客户群User详情
     * @author: jin.wang03
     * @date: 2023/10/30 9:41
     * @since JDK 1.8
     */
    private List<CmWechatGroupUserPO> getCmWechatGroupUserPos(String chatId) {
        List<CmWechatGroupUserPO> resutList = new ArrayList<>();

        int page = 1;
        while (true) {
            PageMethod.startPage(page, 500);
            List<CmWechatGroupUserPO> wechatGroupUserPoList = cmWechatGroupUserRepository.listByChatId(chatId,null);
            if (CollectionUtils.isEmpty(wechatGroupUserPoList)) {
                break;
            }
            resutList.addAll(wechatGroupUserPoList);
            page++;
        }
        return resutList;
    }
}